<script setup lang="ts">
// 住宿详单组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, watch, nextTick } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

// 类型定义
interface AccommodationDetailItem {
  tempId: string; // 临时ID
  serialNumber: number; // 序号
  occurDate: Dayjs | string | null; // 发生时间
  checkInPersonNum: number | null; // 签到人数
  detailPersonNum: number | null; // 详单人数
  comparisonResult: number; // 比对结果 (0:不一致,1:一致)
  paths: string[]; // 附件路径数组
  // 内部使用但不传递给父组件
  _attachments?: UploadFile[]; // 附件文件列表（仅用于界面显示）
}

// 考核人员类型
interface AssessmentPerson {
  id: number;
  name: string;
  checkIn: string; // 签到人员名称
}

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

const props = defineProps({
  accommodationDetailList: {
    type: Array as () => AccommodationDetailItem[],
    default: () => [],
  },
});

const emit = defineEmits(['accommodationDetailEmit']);

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 考核相关数据
const assessmentVisible = ref(false);
const currentAssessmentDate = ref<string | null>(null);
const assessmentPersonList = ref<AssessmentPerson[]>([]);
const selectedTabKey = ref('2025-02-02');

// 考核表格列定义
const assessmentColumns = [
  {
    title: '序号',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '入住人员',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '签到人员',
    dataIndex: 'checkIn',
    key: 'checkIn',
    width: 100,
    align: 'center',
  },
];

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 从缓存数据初始化住宿详单
const initAccommodationDetailFromCache = (cacheData: AccommodationDetailItem[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }

  console.log('🔄 住宿详单 - 开始从缓存恢复数据:', cacheData);

  // 处理缓存数据，将 paths 转换为 _attachments
  const processedData = cacheData.map((item: AccommodationDetailItem, index: number) => {
    const processedItem = { ...item };

    // 处理附件数据 - 将字符串路径转换为 UploadFile 对象
    if (item.paths && Array.isArray(item.paths) && item.paths.length > 0) {
      processedItem._attachments = item.paths.map((path: string, fileIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        return {
          uid: `${item.tempId || Date.now()}_cache_${fileIndex}`,
          name: path.split('/').pop() || `住宿详单${fileIndex + 1}`,
          status: 'done' as const,
          url: baseUrl + processedPath,
          filePath: processedPath,
          fileName: path.split('/').pop() || `住宿详单${fileIndex + 1}`,
        };
      });
    } else {
      processedItem._attachments = [];
    }

    // 处理日期格式
    if (item.occurDate && typeof item.occurDate === 'string') {
      processedItem.occurDate = item.occurDate; // 保持字符串格式
    }

    console.log(`✅ 住宿详单 ${index + 1} 恢复完成:`, processedItem);
    return processedItem;
  });

  isInitialized.value = true;
  console.log('🎉 住宿详单 - 缓存数据恢复完成，共恢复', processedData.length, '条记录');

  // 发射处理后的数据到父组件
  emit('accommodationDetailEmit', processedData);
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '未知文件';
  
  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;
  
  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
  
  return `${truncatedName}.${extension}`;
};

// 新增住宿详单
const handleAddAccommodationDetail = () => {
  const newSerialNumber =
    props.accommodationDetailList.length > 0
      ? Math.max(...props.accommodationDetailList.map((item) => item.serialNumber || 0)) + 1
      : 1;

  const newItem: AccommodationDetailItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: newSerialNumber,
    occurDate: null,
    checkInPersonNum: null,
    detailPersonNum: null,
    comparisonResult: 0, // 默认为不一致(0)
    paths: [],
    _attachments: [],
  };

  const updatedList = [...props.accommodationDetailList, newItem];
  emit('accommodationDetailEmit', updatedList);
};

// 日期格式化
const formatDate = (date: Dayjs | string | null): string | null => {
  if (!date) return null;
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 更新住宿详单字段
const updateAccommodationDetail = (itemId: string, field: string, value: any) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
};

// 检查两个人数是否一致并更新比对结果
const updateComparisonResult = (itemId: string) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      const isMatch =
        item.checkInPersonNum !== null &&
        item.detailPersonNum !== null &&
        item.checkInPersonNum === item.detailPersonNum;
      return { ...item, comparisonResult: isMatch ? 1 : 0 }; // 1: 一致, 0: 不一致
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
};

// 日期变化处理
const handleDateChange = (itemId: string, date: Dayjs | null) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, occurDate: formatDate(date) };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
};

// 文件上传处理
const uploadRequest = (options: any, itemId: string) => {
  // 文件验证
  const file = options.file;
  const isValidType = SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', file);

  // 创建一个临时文件对象用于立即回显
  const tempFileObj: UploadFile = {
    uid: file.uid || Date.now().toString(),
    name: file.name,
    status: 'uploading',
    fileName: file.name,
  };

  // 先更新UI，添加临时文件
  const updatedTempList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        _attachments: [...(item._attachments || []), tempFileObj],
      };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedTempList);

  // 执行上传
  fileApi
    .upload(formData)
    .then((response) => {
      // 上传成功后更新文件对象
      const fileObj: UploadFile = {
        uid: tempFileObj.uid,
        name: file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? response.path : '', // 存储相对路径
        fileName: file.name,
      };

      // 更新文件列表
      const updatedList = props.accommodationDetailList.map((item) => {
        if (item.tempId === itemId) {
          // 找到当前临时文件并替换它
          const updatedAttachments = (item._attachments || []).map((attachment) =>
            attachment.uid === tempFileObj.uid ? fileObj : attachment,
          );

          // 存储完整URL路径，而不仅仅是相对路径
          const fullPath = response.path
            ? response.path.startsWith('/')
              ? baseUrl + response.path
              : baseUrl + '/' + response.path
            : '';

          return {
            ...item,
            _attachments: updatedAttachments,
            paths: [...item.paths, fullPath],
          };
        }
        return item;
      });

      emit('accommodationDetailEmit', updatedList);
      message.success(`文件 ${file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`文件 ${file.name} 上传失败，请重试`);

      // 从列表中移除临时文件
      const failedList = props.accommodationDetailList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            _attachments: (item._attachments || []).filter((file) => file.uid !== tempFileObj.uid),
          };
        }
        return item;
      });

      emit('accommodationDetailEmit', failedList);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除文件
const handleRemoveFile = (file: UploadFile, itemId: string) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      const index = item._attachments?.findIndex(f => f.uid === file.uid) ?? -1;
      if (index > -1) {
        const newAttachments = [...(item._attachments || [])];
        const newPaths = [...item.paths];
        newAttachments.splice(index, 1);
        newPaths.splice(index, 1);

        return {
          ...item,
          _attachments: newAttachments,
          paths: newPaths,
        };
      }
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
  message.success('文件删除成功');
};

// 文件预览
const handlePreviewFile = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 通过路径预览文件
const handlePreviewPath = (path: string, itemId: string, index: number) => {
  if (!path) {
    message.warning('无法打开文件，文件路径不存在');
    return;
  }

  const item = props.accommodationDetailList.find(item => item.tempId === itemId);
  if (item && item._attachments && item._attachments[index]) {
    handlePreviewFile(item._attachments[index]);
  } else {
    // 如果没有关联的附件对象，尝试打开URL
    const fullUrl = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    window.open(fullUrl, '_blank');
  }
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 打开考核弹窗
const handleViewAssessment = (item: AccommodationDetailItem) => {
  currentAssessmentDate.value =
    typeof item.occurDate === 'object' && item.occurDate ? formatDate(item.occurDate) : item.occurDate;

  // 模拟考核数据 - 实际项目中应从API获取
  assessmentPersonList.value = [
    { id: 1, name: '张三', checkIn: '李明' },
    { id: 2, name: '李四', checkIn: '王芳' },
    { id: 3, name: '王五', checkIn: '赵静' },
  ];

  assessmentVisible.value = true;
};

// 关闭考核弹窗
const handleAssessmentCancel = () => {
  assessmentVisible.value = false;
  currentAssessmentDate.value = null;
  assessmentPersonList.value = [];
};

// 切换考核日期Tab
const handleTabChange = (key: string) => {
  selectedTabKey.value = key;

  // 根据不同日期更新考核人员数据 - 实际项目中应从API获取
  if (key === '2025-02-02') {
    assessmentPersonList.value = [
      { id: 1, name: '张三', checkIn: '李明' },
      { id: 2, name: '李四', checkIn: '王芳' },
      { id: 3, name: '王五', checkIn: '赵静' },
    ];
  } else if (key === '2025-02-03') {
    assessmentPersonList.value = [
      { id: 1, name: '张三', checkIn: '李明' },
      { id: 2, name: '李四', checkIn: '王芳' },
      { id: 3, name: '王五', checkIn: '赵静' },
    ];
  } else if (key === '2025-02-04') {
    assessmentPersonList.value = [
      { id: 1, name: '张三', checkIn: '李明' },
      { id: 2, name: '李四', checkIn: '王芳' },
      { id: 3, name: '王五', checkIn: '赵静' },
    ];
  }
};

// 获取提交数据 - 过滤掉_attachments字段
const getSubmitData = () => {
  return props.accommodationDetailList.map(item => {
    const { _attachments, ...submitItem } = item;
    return {
      ...submitItem,
      occurDate: formatDate(submitItem.occurDate), // 确保是字符串格式
      checkInPersonNum: submitItem.checkInPersonNum || 0, // 确保不是null
      detailPersonNum: submitItem.detailPersonNum || 0, // 确保不是null
      comparisonResult: submitItem.comparisonResult, // 保持数字格式 (0:不一致,1:一致)
      paths: submitItem.paths || [] // 确保是数组
    };
  });
};

// 暂存和校验方法（可选实现）
const accommodationDetailTempSave = () => {
  emit('accommodationDetailEmit', props.accommodationDetailList);
};

const accommodationDetailSub = () => {
  let isVerPassed = true;

  if (!props.accommodationDetailList || props.accommodationDetailList.length === 0) {
    message.error('请添加住宿详单！');
    isVerPassed = false;
    return isVerPassed;
  }

  // 验证每个条目的必填字段
  for (const item of props.accommodationDetailList) {
    if (!item.occurDate) {
      message.error(`第${item.serialNumber}条：请选择日期！`);
      isVerPassed = false;
      break;
    }
    if (item.checkInPersonNum === null) {
      message.error(`第${item.serialNumber}条：请输入签到人数！`);
      isVerPassed = false;
      break;
    }
    if (item.detailPersonNum === null) {
      message.error(`第${item.serialNumber}条：请输入详单人数！`);
      isVerPassed = false;
      break;
    }
  }

  if (isVerPassed) {
    accommodationDetailTempSave();
  }

  return isVerPassed;
};

// 监听 accommodationDetailList 变化，处理缓存数据回显
watch(() => props.accommodationDetailList, (newList) => {
  if (newList && newList.length > 0 && !isInitialized.value) {
    console.log('📥 住宿详单 - 检测到缓存数据:', newList);

    // 检查是否需要处理附件回显
    const needsProcessing = newList.some(item =>
      item.paths && item.paths.length > 0 && (!item._attachments || item._attachments.length === 0)
    );

    if (needsProcessing) {
      initAccommodationDetailFromCache(newList);
    } else {
      isInitialized.value = true;
    }
  }
}, { immediate: true, deep: true });

// 暴露方法给父组件
defineExpose({ accommodationDetailSub, accommodationDetailTempSave, getSubmitData });

onMounted(() => {
  // 等待一下，让 watch 先执行
  nextTick(() => {
    // 只有在没有缓存数据时才初始化空数据
    if (props.accommodationDetailList.length === 0 && !isInitialized.value) {
      handleAddAccommodationDetail();
      isInitialized.value = true;
    }
  });
});
</script>

<template>
  <!-- 住宿详单 -->
  <div class="scheme_accommodation_detail">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>住宿详单</span>
    </div>

    <!-- 可编辑表格 -->
    <div class="info-table-wrapper accommodation-table">
      <div class="table-header">
        <div class="col-serial">序号</div>
        <div class="col-attachment">附件</div>
        <div class="col-occurdate">发生时间</div>
        <div class="col-checkinperson">签到人数</div>
        <div class="col-detailperson">详单人数</div>
        <div class="col-comparison">比对结果</div>
        <div class="col-actions">操作</div>
      </div>
      
      <div class="table-body">
        <div v-for="item in accommodationDetailList" :key="item.tempId" class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 附件 -->
          <div class="col-attachment">
            <div class="attachment-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item._attachments && item._attachments.length > 0">
                <a-tag
                  v-for="file in item._attachments"
                  :key="file.uid"
                  closable
                  class="file-tag"
                  @click="() => handlePreviewFile(file)"
                  @close="() => handleRemoveFile(file, item.tempId)"
                >
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 -->
              <a-upload
                :file-list="[]"
                :custom-request="(options: any) => uploadRequest(options, item.tempId)"
                :multiple="true"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
              >
                <a-button size="small" type="link" :loading="uploadLoading">
                  <upload-outlined />
                  上传
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 发生时间 -->
          <div class="col-occurdate">
            <a-date-picker
              :value="typeof item.occurDate === 'string' ? (item.occurDate ? dayjs(item.occurDate) : null) : item.occurDate"
              format="YYYY-MM-DD"
              placeholder="选择日期"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(value: Dayjs | null) => handleDateChange(item.tempId, value)"
            />
          </div>

          <!-- 签到人数 -->
          <div class="col-checkinperson">
            <a-input-number
              v-model:value="item.checkInPersonNum"
              :min="0"
              placeholder="签到人数"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(value: number | null) => updateAccommodationDetail(item.tempId, 'checkInPersonNum', value)"
              @blur="() => updateComparisonResult(item.tempId)"
            />
          </div>

          <!-- 详单人数 -->
          <div class="col-detailperson">
            <a-input-number
              v-model:value="item.detailPersonNum"
              :min="0"
              placeholder="详单人数"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(value: number | null) => updateAccommodationDetail(item.tempId, 'detailPersonNum', value)"
              @blur="() => updateComparisonResult(item.tempId)"
            />
          </div>

          <!-- 比对结果 -->
          <div class="col-comparison">
            <a-button size="small" :type="item.comparisonResult === 1 ? 'primary' : 'danger'" :ghost="true" disabled>
              {{ item.comparisonResult === 1 ? '一致' : '不一致' }}
            </a-button>
          </div>

          <!-- 操作 -->
          <div class="col-actions">
            <a-button
              type="link"
              size="small"
              @click="() => handleViewAssessment(item)"
            >
              查看核对
            </a-button>
           
          </div>
        </div>

        <!-- 添加按钮行 -->
        <div class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddAccommodationDetail">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>新增住宿详单</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal
      v-model:open="previewVisible"
      title="文件预览"
      :footer="null"
      width="80%"
      @cancel="handlePreviewCancel"
    >
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <!-- 图片预览 -->
            <img
              v-if="previewFile.name && (previewFile.name.toLowerCase().includes('.jpg') || previewFile.name.toLowerCase().includes('.jpeg') || previewFile.name.toLowerCase().includes('.png') || previewFile.name.toLowerCase().includes('.gif'))"
              :src="previewFile.url"
              alt="预览图片"
              style="max-width: 100%; max-height: 500px; object-fit: contain;"
            />
            <!-- PDF预览 -->
            <iframe
              v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url"
              style="width: 100%; height: 500px; border: none;"
            ></iframe>
            <!-- 其他文件类型显示下载链接 -->
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile">
                下载文件
              </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p v-if="previewFile && previewFile.status === 'uploading'">文件正在上传中，请稍候...</p>
              <p v-else>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>

    <!-- 考核详情弹窗 -->
    <a-modal
      v-model:open="assessmentVisible"
      title="考核详情"
      :footer="null"
      width="600px"
      @cancel="handleAssessmentCancel"
    >
      <div class="assessment-detail-content">
        <a-tabs v-model:activeKey="selectedTabKey" @change="handleTabChange">
          <a-tab-pane key="2025-02-02" tab="2025-02-02">
            <a-table
              :dataSource="assessmentPersonList"
              :columns="assessmentColumns"
              :pagination="false"
              :rowKey="(record: AssessmentPerson) => record.id"
              bordered
              size="middle"
            >
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2025-02-03" tab="2025-02-03">
            <a-table
              :dataSource="assessmentPersonList"
              :columns="assessmentColumns"
              :pagination="false"
              :rowKey="(record: AssessmentPerson) => record.id"
              bordered
              size="middle"
            >
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2025-02-04" tab="2025-02-04">
            <a-table
              :dataSource="assessmentPersonList"
              :columns="assessmentColumns"
              :pagination="false"
              :rowKey="(record: AssessmentPerson) => record.id"
              bordered
              size="middle"
            >
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
.scheme_accommodation_detail {
  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-radius: 0;
    margin-bottom: 0;

    &.accommodation-table {
      width: 100%;
    }

    .table-header {
      display: flex;
      background-color: #fafafa;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-attachment {
        width: 250px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-occurdate {
        width: 140px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-checkinperson {
        width: 120px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-detailperson {
        width: 120px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-comparison {
        width: 100px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-actions {
        width: 160px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 38px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }
        .col-attachment {
          width: 250px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .attachment-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 10px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                padding: 2px 4px;
                margin: 1px;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
        .col-occurdate {
          width: 140px;
        }
        .col-checkinperson {
          width: 120px;
        }
        .col-detailperson {
          width: 120px;
        }
        .col-comparison {
          width: 100px;
        }
        .col-actions {
          width: 160px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }

  .assessment-detail-content {
    .assessment-table {
      .person-info {
        display: flex;

        .person-id {
          margin-right: 8px;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
